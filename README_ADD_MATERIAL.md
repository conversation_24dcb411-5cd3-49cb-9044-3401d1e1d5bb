# 项目物料添加工具

这个工具用于向项目中添加物料信息，支持单个添加和批量添加两种方式。

## 功能说明

1. 单个项目添加物料：通过API直接添加物料到指定项目
2. 批量添加物料：通过Excel文件批量添加物料到多个项目
3. 生成Excel模板，用于填写批量添加的物料信息
4. 生成处理报告，记录添加结果

## 使用前准备

安装必要的依赖：

```bash
pip install pandas openpyxl requests
```

## 使用方法

### 单个项目添加物料

直接运行`api_add_material.py`脚本：

```bash
python api_add_material.py
```

默认会使用脚本中预设的项目ID和物料信息。如需修改，请编辑脚本中的`care_service_id`和`materials`变量。

### 批量添加物料

#### 第一步：生成Excel模板

直接运行`excel_material_processor.py`脚本，不带任何参数：

```bash
python excel_material_processor.py
```

这将生成一个名为`项目物料模板.xlsx`的Excel文件。

#### 第二步：填写物料信息

打开生成的Excel模板，按照以下格式填写物料信息：

| 项目ID | 耗材ID | 耗材名称 | 规格 | 单位 | 数量 | 单价 | 是否必填 | 是否默认带出 |
|--------|--------|----------|------|------|------|------|----------|--------------|
| 项目的唯一标识 | 耗材的唯一标识 | 耗材名称 | 规格信息 | 单位 | 数量 | 单价 | 1或0 | 1或0 |

说明：
- 项目ID：要添加物料的项目ID
- 耗材ID：要添加的耗材ID
- 是否必填：1表示必填，0表示非必填
- 是否默认带出：1表示划扣默认带出耗材，0表示不带出

#### 第三步：运行批量添加

使用填写好的Excel文件运行脚本：

```bash
python excel_material_processor.py 项目物料模板.xlsx
```

或者指定其他Excel文件：

```bash
python excel_material_processor.py 你的文件名.xlsx
```

#### 第四步：查看结果

脚本执行完成后，将生成一个名为`物料添加报告_时间戳.json`的JSON文件，包含处理结果统计和详细信息。

## API接口说明

添加物料的API接口：

- URL: https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/addMaterial
- 方法: POST
- 认证: JWT token (x-access-token头部)
- 请求体格式:

```json
{
  "careServiceId": "项目ID",
  "materialParams": [
    {
      "skuId": "耗材ID",
      "materialName": "耗材名称",
      "spec": "规格",
      "unit": "单位",
      "usedQuantity": 数量,
      "costUnitPrice": 单价,
      "requiredFlag": 是否必填(0或1),
      "executionFlag": 是否默认带出(0或1)
    }
  ]
}
```

## 注意事项

1. 确保填写的项目ID和耗材ID是正确的
2. 数量和单价必须是数字类型
3. 是否必填和是否默认带出必须是0或1
4. 每次运行脚本将自动获取新的Token
5. 如果某个项目添加物料失败，会在控制台输出错误信息，但不会影响其他项目的处理 