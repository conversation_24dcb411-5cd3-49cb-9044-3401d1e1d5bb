# 项目物料高效批量导入工具

这个工具用于高效地从Excel表格批量导入物料信息到项目中，针对hc1.xlsx表格格式进行了优化。

## 功能特点

1. **高效并发处理**：使用多线程并发处理多个项目，大幅提高导入速度
2. **批量处理**：对大量物料进行分批处理，避免单次请求数据过大
3. **进度显示**：实时显示处理进度和剩余时间
4. **连接复用**：使用会话对象复用HTTP连接，减少连接建立的开销
5. **详细报告**：生成包含处理结果和性能统计的详细报告

## 使用前准备

安装必要的依赖：

```bash
pip install pandas openpyxl requests tqdm
```

## Excel表格格式要求

表格必须包含以下列：

| 列名 | 对应API字段 | 说明 |
|-----|------------|-----|
| 项目id | careServiceId | 项目的唯一标识 |
| 耗材id | skuId | 耗材的唯一标识 |
| 耗材名称 | materialName | 耗材名称 |
| 耗材规格 | spec | 规格信息 |
| 耗材单位 | unit | 单位 |
| 数量 | usedQuantity | 数量，必须为数字 |
| 单价 | costUnitPrice | 单价，必须为数字 |
| 是否必填 | requiredFlag | 1表示必填，0表示非必填 |
| 默认出库 | executionFlag | 1表示划扣默认带出耗材，0表示不带出 |

## 使用方法

### 基本用法

```bash
python batch_material_import.py hc1.xlsx
```

### 高级用法（调整并发数和批处理大小）

```bash
python batch_material_import.py hc1.xlsx [并发线程数] [批处理大小]
```

例如，使用20个线程，每批处理100个物料：

```bash
python batch_material_import.py hc1.xlsx 20 100
```

## 性能调优建议

1. **并发线程数**：
   - 对于网络延迟高的环境，可以增加线程数（如15-20）
   - 对于网络带宽有限的环境，建议使用较少的线程数（如5-10）

2. **批处理大小**：
   - 对于服务器处理能力强的环境，可以增加批处理大小（如80-100）
   - 对于服务器负载较高的环境，建议减小批处理大小（如30-50）

3. **最佳组合**：
   - 通常10-15个线程，每批50-80个物料是较好的平衡点
   - 可以通过多次测试找到最适合您环境的参数组合

## 处理报告

执行完成后，会生成一个JSON格式的处理报告，包含以下信息：

- 总项目数和物料数
- 成功和失败的项目数和物料数
- 开始和结束时间
- 总处理时间
- 每个项目的详细处理结果

## 注意事项

1. 确保Excel表格中的列名与要求一致
2. 数量和单价必须是有效的数字
3. 是否必填和默认出库必须是0或1
4. 对于大量数据，建议先小批量测试后再进行完整导入
5. 如果导入过程中断，可以根据报告中的失败记录进行重试 