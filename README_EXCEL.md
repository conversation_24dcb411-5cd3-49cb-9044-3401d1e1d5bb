# 项目信息批量查询工具

这个工具用于批量查询项目信息，通过Excel文件中的项目编码列表进行查询，并生成包含详细项目信息的Excel报告。

## 功能说明

1. 生成Excel模板，用于填写项目编码
2. 批量查询多个项目的信息
3. 提取项目的基本信息和物料清单
4. 生成包含项目信息和物料信息的Excel报告

## 使用前准备

安装必要的依赖：

```bash
pip install pandas openpyxl requests
```

## 使用方法

### 第一步：生成Excel模板

直接运行脚本，不带任何参数：

```bash
python excel_batch_processor.py
```

这将生成一个名为`项目编码模板.xlsx`的Excel文件。

### 第二步：填写项目编码

打开生成的Excel模板，在"项目编码"列中填入要查询的项目编码，可以填入多行进行批量查询。

### 第三步：运行批量查询

使用填写好的Excel文件运行脚本：

```bash
python excel_batch_processor.py 项目编码模板.xlsx
```

或者指定其他Excel文件：

```bash
python excel_batch_processor.py 你的文件名.xlsx
```

### 第四步：查看结果

脚本执行完成后，将生成一个名为`项目信息报告_时间戳.xlsx`的Excel文件，包含以下内容：

1. "项目信息"工作表：包含项目的基本信息（ID、名称、编码、价格等）
2. "物料信息"工作表：包含所有项目关联的物料详细信息

## 注意事项

1. Excel文件必须包含名为"项目编码"的列
2. 每次运行脚本将自动获取新的Token
3. 查询结果会保存在当前目录下的Excel文件中
4. 如果某个项目查询失败，会在控制台输出错误信息，但不会影响其他项目的查询

## 示例

查询结果示例：

### 项目信息表
| id | name | code | englishName | category | price | enable | materialsCount |
|----|------|------|-------------|----------|-------|--------|----------------|
| 4aeefe917d9a4c01a73936a1d6dedbee | 测试 | 250703002651 |  | 无分类 | 0.0 | 是 | 7 |

### 物料信息表
| projectId | projectName | projectCode | materialId | materialName | spec | unit | usedQuantity | costUnitPrice |
|-----------|-------------|-------------|------------|--------------|------|------|--------------|---------------|
| 4aeefe917d9a4c01a73936a1d6dedbee | 测试 | 250703002651 | 93feca66b4274f75b262f2c163cb69a8 | 酒精消毒片（球、棉签） | 袋装通用型3*6 1片/袋 50袋/盒 | 盒 | 1 | 0.0 |
| ... | ... | ... | ... | ... | ... | ... | ... | ... | 