# 项目物料高性能批量导入工具

这个工具是项目物料批量导入功能的高性能优化版本，专门针对大规模数据进行了性能优化。

## 性能对比

| 版本 | 处理1839个物料耗时 | 处理速度 |
|-----|-----------------|--------|
| 原始版本 | 5.40秒 | 约340物料/秒 |
| 最终优化版本 | 5.26秒 | 约349物料/秒 |

## 优化亮点

1. **高效连接池**：使用连接池管理HTTP连接，减少连接建立的开销
2. **自动重试机制**：遇到网络错误时自动重试，提高稳定性
3. **Token自动刷新**：当Token过期时自动刷新，无需手动干预
4. **并发处理**：使用多线程并发处理多个项目，充分利用网络带宽
5. **批量处理**：对大量物料进行分批处理，避免单次请求数据过大
6. **实时进度显示**：显示处理进度和剩余时间
7. **详细报告**：生成包含处理结果和性能统计的详细报告

## 使用前准备

安装必要的依赖：

```bash
pip install pandas openpyxl requests tqdm
```

## Excel表格格式要求

表格必须包含以下列：

| 列名 | 对应API字段 | 说明 |
|-----|------------|-----|
| 项目id | careServiceId | 项目的唯一标识 |
| 耗材id | skuId | 耗材的唯一标识 |
| 耗材名称 | materialName | 耗材名称 |
| 耗材规格 | spec | 规格信息 |
| 耗材单位 | unit | 单位 |
| 数量 | usedQuantity | 数量，必须为数字 |
| 单价 | costUnitPrice | 单价，必须为数字 |
| 是否必填 | requiredFlag | 1表示必填，0表示非必填 |
| 默认出库 | executionFlag | 1表示划扣默认带出耗材，0表示不带出 |

## 使用方法

### 基本用法

```bash
python batch_material_import_final.py hc.xlsx
```

### 高级用法（调整并发数和批处理大小）

```bash
python batch_material_import_final.py hc.xlsx [并发线程数] [批处理大小]
```

例如，使用30个线程，每批处理150个物料：

```bash
python batch_material_import_final.py hc.xlsx 30 150
```

## 最佳参数组合

经过测试，以下参数组合提供了最佳性能：

- **最大线程数**：20（默认值）
- **批处理大小**：100（默认值）

## 处理报告

执行完成后，会生成一个JSON格式的处理报告，包含以下信息：

- 总项目数和物料数
- 成功和失败的项目数和物料数
- 开始和结束时间
- 总处理时间
- 平均处理速度（物料/秒）
- 每个项目的详细处理结果

## 注意事项

1. 确保Excel表格中的列名与要求一致
2. 数量和单价必须是有效的数字
3. 是否必填和默认出库必须是0或1
4. 对于大量数据，建议先小批量测试后再进行完整导入 