import requests
import json
from pprint import pprint

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def add_material(care_service_id, materials, token=None):
    """
    调用API接口添加物料到项目
    
    Args:
        care_service_id (str): 项目ID
        materials (list): 物料参数列表
        token (str, optional): JWT令牌，如果为None则自动获取新的token
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # 如果没有提供token，则获取新的token
    if token is None:
        token = get_new_token()
        if token is None:
            print("获取Token失败，无法继续操作")
            return None
    
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/addMaterial"
    
    # 请求数据
    data = {
        "careServiceId": care_service_id,
        "materialParams": materials
    }
    
    # 请求头
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print("\n=== 添加物料 ===")
    print(f"请求URL: {url}")
    print(f"项目ID: {care_service_id}")
    print(f"物料数量: {len(materials)}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应
        if response.status_code in [200, 201]:
            result = response.json()
            print("请求成功!")
            return result
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def display_result(data):
    """
    格式化显示API返回的结果
    
    Args:
        data (dict): API返回的JSON数据
    """
    if not data:
        print("\n没有获取到数据")
        return
    
    print("\n=== 添加物料结果 ===")
    print(json.dumps(data, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    # 示例项目ID
    care_service_id = "4aeefe917d9a4c01a73936a1d6dedbee"  # 替换为实际项目ID
    
    # 示例物料列表
    materials = [
        {
            "skuId": "93feca66b4274f75b262f2c163cb69a8",  # 替换为实际耗材ID
            "materialName": "酒精消毒片（球、棉签）",
            "spec": "袋装通用型3*6 1片/袋 50袋/盒",
            "unit": "盒",
            "usedQuantity": 1,
            "costUnitPrice": 0.0,
            "requiredFlag": 1,  # 1表示必填，0表示非必填
            "executionFlag": 1  # 1表示划扣默认带出耗材，0表示不带出
        }
    ]
    
    # 获取新的token
    token = get_new_token()
    if token:
        # 调用API添加物料
        result = add_material(care_service_id, materials, token)
        
        # 显示格式化结果
        if result:
            display_result(result)
            
            # 保存完整响应到文件
            with open("api_add_material_response.json", "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("\n完整响应已保存到 api_add_material_response.json 文件") 