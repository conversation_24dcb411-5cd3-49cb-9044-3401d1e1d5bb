import pandas as pd
import os
import json
import requests
import datetime
import time
import concurrent.futures
import sys
from tqdm import tqdm

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def add_material_batch(care_service_id, materials, token, session=None):
    """
    调用API接口添加物料到项目
    
    Args:
        care_service_id (str): 项目ID
        materials (list): 物料参数列表
        token (str): JWT令牌
        session (requests.Session, optional): 请求会话对象，用于复用连接
        
    Returns:
        bool: 是否添加成功
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/addMaterial"
    
    # 请求数据
    data = {
        "careServiceId": care_service_id,
        "materialParams": materials
    }
    
    # 请求头
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    try:
        # 使用会话对象发送请求，如果没有提供则创建新的请求
        if session:
            response = session.post(url, json=data, headers=headers, timeout=10)
        else:
            response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 检查状态码
        if response.status_code in [200, 201, 204]:
            # 成功，可能有内容也可能没有内容
            return True
        else:
            print(f"添加物料失败 (项目ID: {care_service_id}): 状态码 {response.status_code}, 响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常 (项目ID: {care_service_id}): {e}")
        return False

def process_project(args):
    """
    处理单个项目的物料添加
    
    Args:
        args (tuple): 包含项目ID、物料列表、token和session的元组
        
    Returns:
        dict: 处理结果
    """
    care_service_id, materials, token, session = args
    
    start_time = time.time()
    success = add_material_batch(care_service_id, materials, token, session)
    end_time = time.time()
    
    return {
        "project_id": care_service_id,
        "materials_count": len(materials),
        "success": success,
        "time_taken": end_time - start_time
    }

def process_excel_optimized(input_file, max_workers=10, batch_size=50):
    """
    优化的Excel处理函数，使用并发和批处理提高效率
    
    Args:
        input_file (str): 输入Excel文件路径
        max_workers (int): 最大并发工作线程数
        batch_size (int): 每批处理的物料数量
        
    Returns:
        dict: 处理结果统计
    """
    try:
        print(f"开始处理文件: {input_file}")
        start_time = time.time()
        
        # 读取Excel文件，确保字符串列不被转换为数值
        df = pd.read_excel(input_file, dtype={
            '项目id': str,
            '项目编码': str,
            '项目名称': str,
            '耗材id': str,
            '耗材编码': str,
            '耗材名称': str,
            '耗材规格': str,
            '耗材单位': str
        })
        
        # 检查是否包含必要的列
        required_columns = ['项目id', '耗材id', '耗材名称', '耗材规格', '耗材单位', '数量', '单价', '是否必填', '默认出库']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误: Excel文件缺少以下必要列: {', '.join(missing_columns)}")
            return None
        
        # 获取新的token
        token = get_new_token()
        if token is None:
            print("获取Token失败，无法继续操作")
            return None
        
        # 按项目ID分组
        print("按项目ID分组数据...")
        grouped = df.groupby('项目id')
        
        # 统计结果
        results = {
            "total_projects": len(grouped),
            "successful_projects": 0,
            "failed_projects": 0,
            "total_materials": len(df),
            "added_materials": 0,
            "failed_materials": 0,
            "project_details": [],
            "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": None,
            "total_time": None
        }
        
        print(f"找到 {results['total_projects']} 个项目，共 {results['total_materials']} 个物料")
        
        # 准备并发处理的任务
        tasks = []
        session = requests.Session()  # 创建一个会话对象以复用连接
        
        for care_service_id, group in grouped:
            # 准备物料参数
            materials = []
            for _, row in group.iterrows():
                try:
                    material = {
                        "skuId": str(row['耗材id']),
                        "materialName": str(row['耗材名称']),
                        "spec": str(row['耗材规格']),
                        "unit": str(row['耗材单位']),
                        "usedQuantity": float(row['数量']),
                        "costUnitPrice": float(row['单价']),
                        "requiredFlag": int(row['是否必填']),
                        "executionFlag": int(row['默认出库'])
                    }
                    materials.append(material)
                except (ValueError, TypeError) as e:
                    print(f"错误: 处理行 {row.name + 2} 时出错: {e}")
                    results["failed_materials"] += 1
            
            # 如果物料数量超过batch_size，则分批处理
            if len(materials) > batch_size:
                for i in range(0, len(materials), batch_size):
                    batch = materials[i:i+batch_size]
                    tasks.append((str(care_service_id), batch, token, session))
            else:
                tasks.append((str(care_service_id), materials, token, session))
        
        # 使用线程池并发处理
        print(f"开始并发处理，最大线程数: {max_workers}，批处理大小: {batch_size}")
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 使用tqdm显示进度条
            project_results = list(tqdm(executor.map(process_project, tasks), total=len(tasks), desc="处理进度"))
        
        # 汇总结果
        for result in project_results:
            results["project_details"].append(result)
            
            if result["success"]:
                results["successful_projects"] += 1
                results["added_materials"] += result["materials_count"]
            else:
                results["failed_projects"] += 1
                results["failed_materials"] += result["materials_count"]
        
        # 计算总耗时
        end_time = time.time()
        results["end_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        results["total_time"] = end_time - start_time
        
        return results
            
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_report(results):
    """
    生成处理报告
    
    Args:
        results (dict): 处理结果统计
        
    Returns:
        str: 报告文件路径
    """
    if not results:
        return None
    
    # 生成时间戳
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    report_path = f"物料添加报告_{timestamp}.json"
    
    # 保存报告
    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 处理结果 ===")
    print(f"总项目数: {results['total_projects']}")
    print(f"成功项目数: {results['successful_projects']}")
    print(f"失败项目数: {results['failed_projects']}")
    print(f"总物料数: {results['total_materials']}")
    print(f"添加成功物料数: {results['added_materials']}")
    print(f"添加失败物料数: {results['failed_materials']}")
    print(f"开始时间: {results['start_time']}")
    print(f"结束时间: {results['end_time']}")
    print(f"总耗时: {results['total_time']:.2f} 秒")
    
    print(f"\n详细报告已保存到: {report_path}")
    
    return report_path

def main():
    """
    主函数
    """
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("错误: 请提供Excel文件路径")
        print("用法: python batch_material_import.py <excel_file> [max_workers] [batch_size]")
        print("示例: python batch_material_import.py hc1.xlsx 10 50")
        return
    
    # 获取参数
    input_file = sys.argv[1]
    
    # 可选参数：最大线程数和批处理大小
    max_workers = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    batch_size = int(sys.argv[3]) if len(sys.argv) > 3 else 50
    
    if not os.path.exists(input_file):
        print(f"错误: 文件 '{input_file}' 不存在")
        return
    
    print(f"处理文件: {input_file}")
    print(f"最大线程数: {max_workers}")
    print(f"批处理大小: {batch_size}")
    
    results = process_excel_optimized(input_file, max_workers, batch_size)
    
    if results:
        generate_report(results)

if __name__ == "__main__":
    main() 