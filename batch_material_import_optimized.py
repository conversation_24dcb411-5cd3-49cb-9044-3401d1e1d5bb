import pandas as pd
import os
import json
import requests
import datetime
import time
import concurrent.futures
import sys
from tqdm import tqdm
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 全局变量
TOKEN = None
SESSION = None

def setup_session():
    """
    设置带有连接池和重试机制的会话对象
    
    Returns:
        requests.Session: 配置好的会话对象
    """
    session = requests.Session()
    
    # 设置连接池
    adapter = HTTPAdapter(
        pool_connections=100,  # 连接池大小
        pool_maxsize=100,      # 最大连接数
        max_retries=Retry(
            total=3,           # 最大重试次数
            backoff_factor=0.5,  # 重试间隔增长因子
            status_forcelist=[500, 502, 503, 504]  # 需要重试的HTTP状态码
        )
    )
    
    # 为所有HTTP和HTTPS请求配置适配器
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    
    return session

def get_token():
    """
    获取全局token，如果不存在则创建新的
    
    Returns:
        str: JWT令牌
    """
    global TOKEN
    if TOKEN is None:
        TOKEN = get_new_token()
    return TOKEN

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    global SESSION
    if SESSION is None:
        SESSION = setup_session()
        
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("获取Token中...")
    
    try:
        # 发送POST请求
        response = SESSION.post(url, json=data, headers=headers, timeout=10)
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容")
                return None
        else:
            print(f"请求失败: 状态码 {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def add_material_batch(care_service_id, materials):
    """
    调用API接口添加物料到项目
    
    Args:
        care_service_id (str): 项目ID
        materials (list): 物料参数列表
        
    Returns:
        bool: 是否添加成功
    """
    global SESSION
    if SESSION is None:
        SESSION = setup_session()
        
    token = get_token()
    if token is None:
        return False
        
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/addMaterial"
    
    # 请求数据
    data = {
        "careServiceId": care_service_id,
        "materialParams": materials
    }
    
    # 请求头
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    try:
        # 使用会话对象发送请求
        response = SESSION.post(url, json=data, headers=headers, timeout=10)
        
        # 检查状态码
        if response.status_code in [200, 201, 204]:
            # 成功
            return True
        else:
            # 如果是401错误，可能是token过期，尝试刷新token
            if response.status_code == 401:
                global TOKEN
                TOKEN = get_new_token()  # 刷新token
                if TOKEN:
                    # 使用新token重试
                    headers["x-access-token"] = TOKEN
                    response = SESSION.post(url, json=data, headers=headers, timeout=10)
                    return response.status_code in [200, 201, 204]
                
            print(f"添加物料失败 (项目ID: {care_service_id}): 状态码 {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常 (项目ID: {care_service_id}): {e}")
        return False

def process_project(args):
    """
    处理单个项目的物料添加
    
    Args:
        args (tuple): 包含项目ID和物料列表的元组
        
    Returns:
        dict: 处理结果
    """
    care_service_id, materials = args
    
    start_time = time.time()
    success = add_material_batch(care_service_id, materials)
    end_time = time.time()
    
    return {
        "project_id": care_service_id,
        "materials_count": len(materials),
        "success": success,
        "time_taken": end_time - start_time
    }

def chunk_dataframe(df, chunk_size=1000):
    """
    将大型DataFrame分块处理，减少内存使用
    
    Args:
        df (pandas.DataFrame): 输入DataFrame
        chunk_size (int): 每块的行数
        
    Yields:
        pandas.DataFrame: DataFrame的子集
    """
    for i in range(0, len(df), chunk_size):
        yield df.iloc[i:i+chunk_size]

def process_excel_highly_optimized(input_file, max_workers=30, batch_size=200, chunk_size=5000):
    """
    高度优化的Excel处理函数
    
    Args:
        input_file (str): 输入Excel文件路径
        max_workers (int): 最大并发工作线程数
        batch_size (int): 每批处理的物料数量
        chunk_size (int): 每次处理的Excel行数
        
    Returns:
        dict: 处理结果统计
    """
    global SESSION
    SESSION = setup_session()
    
    try:
        print(f"开始处理文件: {input_file}")
        total_start_time = time.time()
        
        # 统计结果
        results = {
            "total_projects": 0,
            "successful_projects": 0,
            "failed_projects": 0,
            "total_materials": 0,
            "added_materials": 0,
            "failed_materials": 0,
            "project_details": [],
            "start_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": None,
            "total_time": None
        }
        
        # 获取token
        global TOKEN
        TOKEN = get_new_token()
        if TOKEN is None:
            print("获取Token失败，无法继续操作")
            return None
        
        # 读取Excel文件
        print("读取Excel文件...")
        df = pd.read_excel(input_file, dtype={
            '项目id': str,
            '项目编码': str,
            '项目名称': str,
            '耗材id': str,
            '耗材编码': str,
            '耗材名称': str,
            '耗材规格': str,
            '耗材单位': str
        })
        
        print(f"文件总行数: {len(df)}")
        
        # 分块处理DataFrame
        chunk_id = 0
        
        # 使用tqdm创建总进度条
        with tqdm(total=len(df), desc="总进度") as pbar:
            # 分块处理
            for chunk in chunk_dataframe(df, chunk_size):
                chunk_id += 1
                chunk_start_time = time.time()
                
                # 按项目ID分组
                grouped = chunk.groupby('项目id')
                
                # 更新统计信息
                results["total_projects"] += len(grouped)
                results["total_materials"] += len(chunk)
                
                print(f"\n处理数据块 {chunk_id}，包含 {len(grouped)} 个项目，{len(chunk)} 个物料")
                
                # 准备并发处理的任务
                tasks = []
                
                for care_service_id, group in grouped:
                    # 准备物料参数
                    materials = []
                    for _, row in group.iterrows():
                        try:
                            material = {
                                "skuId": str(row['耗材id']),
                                "materialName": str(row['耗材名称']),
                                "spec": str(row['耗材规格']),
                                "unit": str(row['耗材单位']),
                                "usedQuantity": float(row['数量']),
                                "costUnitPrice": float(row['单价']),
                                "requiredFlag": int(row['是否必填']),
                                "executionFlag": int(row['默认出库'])
                            }
                            materials.append(material)
                        except (ValueError, TypeError) as e:
                            print(f"错误: 处理行时出错: {e}")
                            results["failed_materials"] += 1
                    
                    # 如果物料数量超过batch_size，则分批处理
                    if len(materials) > batch_size:
                        for i in range(0, len(materials), batch_size):
                            batch = materials[i:i+batch_size]
                            tasks.append((str(care_service_id), batch))
                    else:
                        tasks.append((str(care_service_id), materials))
                
                # 使用线程池并发处理
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 使用tqdm显示进度条
                    chunk_results = list(tqdm(
                        executor.map(process_project, tasks), 
                        total=len(tasks), 
                        desc=f"块 {chunk_id} 进度"
                    ))
                
                # 汇总结果
                for result in chunk_results:
                    results["project_details"].append(result)
                    
                    if result["success"]:
                        results["successful_projects"] += 1
                        results["added_materials"] += result["materials_count"]
                    else:
                        results["failed_projects"] += 1
                        results["failed_materials"] += result["materials_count"]
                
                # 更新总进度条
                pbar.update(len(chunk))
                
                # 打印当前块处理时间
                chunk_end_time = time.time()
                print(f"块 {chunk_id} 处理完成，耗时: {chunk_end_time - chunk_start_time:.2f} 秒")
                
                # 每处理完一个块，保存一次中间结果
                interim_results = results.copy()
                interim_results["end_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                interim_results["total_time"] = time.time() - total_start_time
                
                # 生成中间报告
                interim_timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                interim_report_path = f"中间报告_块{chunk_id}_{interim_timestamp}.json"
                with open(interim_report_path, "w", encoding="utf-8") as f:
                    json.dump(interim_results, f, ensure_ascii=False, indent=2)
        
        # 计算总耗时
        end_time = time.time()
        results["end_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        results["total_time"] = end_time - total_start_time
        
        return results
            
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_report(results):
    """
    生成处理报告
    
    Args:
        results (dict): 处理结果统计
        
    Returns:
        str: 报告文件路径
    """
    if not results:
        return None
    
    # 生成时间戳
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    report_path = f"物料添加报告_{timestamp}.json"
    
    # 保存报告
    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 处理结果 ===")
    print(f"总项目数: {results['total_projects']}")
    print(f"成功项目数: {results['successful_projects']}")
    print(f"失败项目数: {results['failed_projects']}")
    print(f"总物料数: {results['total_materials']}")
    print(f"添加成功物料数: {results['added_materials']}")
    print(f"添加失败物料数: {results['failed_materials']}")
    print(f"开始时间: {results['start_time']}")
    print(f"结束时间: {results['end_time']}")
    print(f"总耗时: {results['total_time']:.2f} 秒")
    
    # 计算每秒处理的物料数
    if results['total_time'] > 0:
        materials_per_second = results['added_materials'] / results['total_time']
        print(f"平均处理速度: {materials_per_second:.2f} 物料/秒")
    
    print(f"\n详细报告已保存到: {report_path}")
    
    return report_path

def main():
    """
    主函数
    """
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("错误: 请提供Excel文件路径")
        print("用法: python batch_material_import_optimized.py <excel_file> [max_workers] [batch_size] [chunk_size]")
        print("示例: python batch_material_import_optimized.py hc.xlsx 30 200 5000")
        return
    
    # 获取参数
    input_file = sys.argv[1]
    
    # 可选参数：最大线程数、批处理大小和分块大小
    max_workers = int(sys.argv[2]) if len(sys.argv) > 2 else 30
    batch_size = int(sys.argv[3]) if len(sys.argv) > 3 else 200
    chunk_size = int(sys.argv[4]) if len(sys.argv) > 4 else 5000
    
    if not os.path.exists(input_file):
        print(f"错误: 文件 '{input_file}' 不存在")
        return
    
    print(f"处理文件: {input_file}")
    print(f"最大线程数: {max_workers}")
    print(f"批处理大小: {batch_size}")
    print(f"分块大小: {chunk_size}")
    
    # 设置更大的递归限制，避免处理大文件时出现递归错误
    sys.setrecursionlimit(10000)
    
    # 开始处理
    results = process_excel_highly_optimized(input_file, max_workers, batch_size, chunk_size)
    
    if results:
        generate_report(results)

if __name__ == "__main__":
    main() 