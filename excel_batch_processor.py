import pandas as pd
import os
import sys
import json
import requests
import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def get_project_info(keyword, token):
    """
    调用API接口获取产品服务信息
    
    Args:
        keyword (str): 查询关键字
        token (str): JWT令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print(f"\n查询项目 {keyword}")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            return result
        else:
            print(f"查询失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def extract_project_info(project_data):
    """
    从API响应中提取项目信息
    
    Args:
        project_data (dict): API响应数据
        
    Returns:
        dict: 提取的项目信息，如果没有找到则返回None
    """
    if not project_data or "content" not in project_data or not project_data["content"]:
        return None
    
    project = project_data["content"][0]  # 获取第一个项目
    
    # 基本信息
    info = {
        "id": project.get("id", ""),
        "name": project.get("name", ""),
        "code": project.get("code", ""),
        "englishName": project.get("englishName", ""),
        "price": project.get("price", 0),
        "enable": "是" if project.get("enable", False) else "否",
        "materials": []
    }
    
    # 分类信息
    if "category" in project and project["category"]:
        info["category"] = project["category"].get("name", "无分类")
    else:
        info["category"] = "无分类"
    
    # 物料信息
    if "materialDtos" in project and project["materialDtos"]:
        for material in project["materialDtos"]:
            material_info = {
                "id": material.get("id", ""),
                "materialId": material.get("materialId", ""),
                "materialName": material.get("materialName", ""),
                "spec": material.get("spec", ""),
                "unit": material.get("unit", ""),
                "usedQuantity": material.get("usedQuantity", 0),
                "costUnitPrice": material.get("costUnitPrice", 0)
            }
            info["materials"].append(material_info)
    
    return info

def create_excel_template():
    """
    创建Excel模板文件
    
    Returns:
        str: 创建的Excel文件路径
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "项目编码"
    
    # 设置列宽
    ws.column_dimensions['A'].width = 20
    ws.column_dimensions['B'].width = 40
    
    # 设置标题样式
    title_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    title_fill = PatternFill(fill_type='solid', fgColor='4472C4')
    title_alignment = Alignment(horizontal='center', vertical='center')
    title_border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )
    
    # 添加标题
    ws['A1'] = '序号'
    ws['B1'] = '项目编码'
    
    for cell in ws[1]:
        cell.font = title_font
        cell.fill = title_fill
        cell.alignment = title_alignment
        cell.border = title_border
    
    # 添加示例数据
    ws['A2'] = 1
    ws['B2'] = '250703002651'  # 确保项目编码为文本格式
    
    # 设置B列为文本格式
    from openpyxl.styles import numbers
    for row in range(2, 100):  # 预设100行
        cell = ws.cell(row=row, column=2)
        cell.number_format = numbers.FORMAT_TEXT
    
    # 添加说明
    ws['A4'] = '说明：'
    ws['A5'] = '1. 在"项目编码"列填入要查询的项目编码'
    ws['A6'] = '2. 可以填入多行项目编码进行批量查询'
    ws['A7'] = '3. 项目编码会被当作文本处理，请确保输入的是完整的编码'
    ws['A8'] = '4. 运行脚本后将生成结果Excel文件'
    
    # 保存模板
    template_path = "项目编码模板.xlsx"
    wb.save(template_path)
    print(f"Excel模板已创建: {template_path}")
    return template_path

def process_excel_and_generate_report(input_file, token):
    """
    处理Excel文件并生成报告
    
    Args:
        input_file (str): 输入Excel文件路径
        token (str): JWT令牌
        
    Returns:
        str: 生成的报告文件路径
    """
    try:
        # 读取Excel文件，确保项目编码列为文本格式
        df = pd.read_excel(input_file, dtype={'项目编码': str})
        
        # 检查是否包含必要的列
        if '项目编码' not in df.columns:
            print("错误: Excel文件必须包含'项目编码'列")
            return None
        
        # 提取项目编码列，并确保是字符串类型
        codes = df['项目编码'].astype(str).tolist()
        # 过滤掉空值和NaN
        codes = [code.strip() for code in codes if code and code.strip() and code.lower() != 'nan']
        # 去掉可能的小数点和零
        codes = [code.split('.')[0] if '.' in code else code for code in codes]
        
        if not codes:
            print("错误: 没有找到有效的项目编码")
            return None
        
        print(f"找到 {len(codes)} 个项目编码，开始处理...")
        
        # 收集项目信息
        projects_info = []
        materials_info = []
        
        for code in codes:
            # 查询项目信息
            project_data = get_project_info(code, token)
            
            if project_data:
                # 提取项目信息
                info = extract_project_info(project_data)
                
                if info:
                    projects_info.append({
                        "id": info["id"],
                        "name": info["name"],
                        "code": info["code"],
                        "englishName": info["englishName"],
                        "category": info["category"],
                        "price": info["price"],
                        "enable": info["enable"],
                        "materialsCount": len(info["materials"])
                    })
                    
                    # 添加物料信息
                    for material in info["materials"]:
                        materials_info.append({
                            "projectId": info["id"],
                            "projectName": info["name"],
                            "projectCode": info["code"],
                            "materialId": material["id"],
                            "materialName": material["materialName"],
                            "spec": material["spec"],
                            "unit": material["unit"],
                            "usedQuantity": material["usedQuantity"],
                            "costUnitPrice": material["costUnitPrice"]
                        })
                else:
                    print(f"未找到项目 {code} 的信息")
            else:
                print(f"查询项目 {code} 失败")
        
        if not projects_info:
            print("没有找到任何项目信息")
            return None
        
        # 生成Excel报告
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        output_file = f"项目信息报告_{timestamp}.xlsx"
        
        with pd.ExcelWriter(output_file) as writer:
            # 项目信息表
            pd.DataFrame(projects_info).to_excel(writer, sheet_name="项目信息", index=False)
            
            # 物料信息表
            if materials_info:
                pd.DataFrame(materials_info).to_excel(writer, sheet_name="物料信息", index=False)
        
        print(f"报告已生成: {output_file}")
        print(f"共处理 {len(codes)} 个项目，找到 {len(projects_info)} 个项目信息，{len(materials_info)} 个物料信息")
        
        return output_file
        
    except Exception as e:
        print(f"处理Excel文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    # 检查命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
        if not os.path.exists(input_file):
            print(f"错误: 找不到文件 {input_file}")
            return
    else:
        # 创建Excel模板
        input_file = create_excel_template()
        print(f"已创建Excel模板: {input_file}")
        print("请在模板中填入项目编码后再次运行此脚本")
        print(f"用法: python {sys.argv[0]} <Excel文件路径>")
        return
    
    # 获取token
    token = get_new_token()
    if not token:
        print("获取Token失败，无法继续操作")
        return
    
    # 处理Excel文件并生成报告
    output_file = process_excel_and_generate_report(input_file, token)
    
    if output_file:
        print(f"\n处理完成! 结果已保存至: {output_file}")
    else:
        print("\n处理失败，未能生成报告")

if __name__ == "__main__":
    main() 