import pandas as pd
import os
import sys
import json
import requests
import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def add_material(care_service_id, materials, token):
    """
    调用API接口添加物料到项目
    
    Args:
        care_service_id (str): 项目ID
        materials (list): 物料参数列表
        token (str): JWT令牌
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/addMaterial"
    
    # 请求数据
    data = {
        "careServiceId": care_service_id,
        "materialParams": materials
    }
    
    # 请求头
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print(f"\n添加物料到项目 {care_service_id}")
    print(f"物料数量: {len(materials)}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 尝试解析JSON响应
        if response.status_code in [200, 201]:
            result = response.json()
            print("添加物料成功!")
            return result
        else:
            print(f"添加物料失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def create_excel_template():
    """
    创建Excel模板文件
    
    Returns:
        str: 创建的Excel文件路径
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "项目物料"
    
    # 设置列宽
    ws.column_dimensions['A'].width = 40
    ws.column_dimensions['B'].width = 40
    ws.column_dimensions['C'].width = 40
    ws.column_dimensions['D'].width = 40
    ws.column_dimensions['E'].width = 20
    ws.column_dimensions['F'].width = 15
    ws.column_dimensions['G'].width = 15
    ws.column_dimensions['H'].width = 15
    
    # 设置标题样式
    title_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    title_fill = PatternFill(fill_type='solid', fgColor='4472C4')
    title_alignment = Alignment(horizontal='center', vertical='center')
    title_border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )
    
    # 添加标题
    ws['A1'] = '项目ID'
    ws['B1'] = '耗材ID'
    ws['C1'] = '耗材名称'
    ws['D1'] = '规格'
    ws['E1'] = '单位'
    ws['F1'] = '数量'
    ws['G1'] = '单价'
    ws['H1'] = '是否必填'
    ws['I1'] = '是否默认带出'
    
    for cell in ws[1]:
        cell.font = title_font
        cell.fill = title_fill
        cell.alignment = title_alignment
        cell.border = title_border
    
    # 添加示例数据
    ws['A2'] = '4aeefe917d9a4c01a73936a1d6dedbee'
    ws['B2'] = '93feca66b4274f75b262f2c163cb69a8'
    ws['C2'] = '酒精消毒片（球、棉签）'
    ws['D2'] = '袋装通用型3*6 1片/袋 50袋/盒'
    ws['E2'] = '盒'
    ws['F2'] = 1
    ws['G2'] = 0.0
    ws['H2'] = 1
    ws['I2'] = 1
    
    # 添加说明
    ws['A4'] = '说明：'
    ws['A5'] = '1. 项目ID为必填项，表示要添加物料的项目'
    ws['A6'] = '2. 耗材ID为必填项，表示要添加的耗材ID'
    ws['A7'] = '3. 其他字段为耗材的详细信息'
    ws['A8'] = '4. 是否必填：1表示必填，0表示非必填'
    ws['A9'] = '5. 是否默认带出：1表示划扣默认带出耗材，0表示不带出'
    ws['A10'] = '6. 可以填入多行数据进行批量添加'
    
    # 保存模板
    template_path = "项目物料模板.xlsx"
    wb.save(template_path)
    print(f"Excel模板已创建: {template_path}")
    return template_path

def process_excel_and_add_materials(input_file):
    """
    处理Excel文件并添加物料
    
    Args:
        input_file (str): 输入Excel文件路径
        
    Returns:
        dict: 处理结果统计
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(input_file)
        
        # 检查是否包含必要的列
        required_columns = ['项目ID', '耗材ID', '耗材名称', '规格', '单位', '数量', '单价', '是否必填', '是否默认带出']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误: Excel文件缺少以下必要列: {', '.join(missing_columns)}")
            return None
        
        # 获取新的token
        token = get_new_token()
        if token is None:
            print("获取Token失败，无法继续操作")
            return None
        
        # 按项目ID分组
        grouped = df.groupby('项目ID')
        
        # 统计结果
        results = {
            "total_projects": len(grouped),
            "successful_projects": 0,
            "failed_projects": 0,
            "total_materials": len(df),
            "added_materials": 0,
            "failed_materials": 0,
            "project_details": []
        }
        
        # 处理每个项目
        for care_service_id, group in grouped:
            print(f"\n处理项目 {care_service_id}")
            
            # 准备物料参数
            materials = []
            for _, row in group.iterrows():
                try:
                    material = {
                        "skuId": str(row['耗材ID']),
                        "materialName": str(row['耗材名称']),
                        "spec": str(row['规格']),
                        "unit": str(row['单位']),
                        "usedQuantity": float(row['数量']),
                        "costUnitPrice": float(row['单价']),
                        "requiredFlag": int(row['是否必填']),
                        "executionFlag": int(row['是否默认带出'])
                    }
                    materials.append(material)
                except (ValueError, TypeError) as e:
                    print(f"错误: 处理行 {row.name + 2} 时出错: {e}")
                    results["failed_materials"] += 1
            
            # 添加物料
            if materials:
                result = add_material(str(care_service_id), materials, token)
                
                project_result = {
                    "project_id": str(care_service_id),
                    "materials_count": len(materials),
                    "success": result is not None
                }
                
                if result:
                    results["successful_projects"] += 1
                    results["added_materials"] += len(materials)
                    project_result["response"] = result
                else:
                    results["failed_projects"] += 1
                    results["failed_materials"] += len(materials)
                
                results["project_details"].append(project_result)
        
        return results
            
    except Exception as e:
        print(f"处理Excel文件时出错: {e}")
        return None

def generate_report(results):
    """
    生成处理报告
    
    Args:
        results (dict): 处理结果统计
        
    Returns:
        str: 报告文件路径
    """
    if not results:
        return None
    
    # 生成时间戳
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    report_path = f"物料添加报告_{timestamp}.json"
    
    # 保存报告
    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n=== 处理结果 ===")
    print(f"总项目数: {results['total_projects']}")
    print(f"成功项目数: {results['successful_projects']}")
    print(f"失败项目数: {results['failed_projects']}")
    print(f"总物料数: {results['total_materials']}")
    print(f"添加成功物料数: {results['added_materials']}")
    print(f"添加失败物料数: {results['failed_materials']}")
    print(f"\n详细报告已保存到: {report_path}")
    
    return report_path

def main():
    """
    主函数
    """
    # 检查命令行参数
    if len(sys.argv) == 1:
        # 没有参数，创建模板
        create_excel_template()
        print("\n请填写模板后，使用以下命令运行批量添加:")
        print(f"python {sys.argv[0]} 项目物料模板.xlsx")
    elif len(sys.argv) == 2:
        # 有参数，处理Excel文件
        input_file = sys.argv[1]
        if not os.path.exists(input_file):
            print(f"错误: 文件 '{input_file}' 不存在")
            return
        
        print(f"处理文件: {input_file}")
        results = process_excel_and_add_materials(input_file)
        
        if results:
            generate_report(results)
    else:
        print("用法:")
        print(f"1. 创建模板: python {sys.argv[0]}")
        print(f"2. 处理Excel: python {sys.argv[0]} <excel_file>")

if __name__ == "__main__":
    main() 