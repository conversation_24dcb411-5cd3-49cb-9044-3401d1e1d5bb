import requests
import json
import pandas as pd

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token}")
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token}")
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def test_add_material_api():
    """
    测试添加物料API接口，使用hc1.xlsx中的实际数据
    """
    # 获取token
    token = get_new_token()
    if not token:
        print("获取Token失败，无法继续测试")
        return
    
    # 读取Excel中的第一行数据
    try:
        df = pd.read_excel('hc1.xlsx')
        row = df.iloc[0]
        
        # API 接口URL
        url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service/addMaterial"
        
        # 使用实际数据构建请求
        data = {
            "careServiceId": str(row['项目id']),
            "materialParams": [
                {
                    "skuId": str(row['耗材id']),
                    "materialName": str(row['耗材名称']),
                    "spec": str(row['耗材规格']),
                    "unit": str(row['耗材单位']),
                    "usedQuantity": float(row['数量']),
                    "costUnitPrice": float(row['单价']),
                    "requiredFlag": int(row['是否必填']),
                    "executionFlag": int(row['默认出库'])
                }
            ]
        }
        
        # 请求头
        headers = {
            "x-access-token": token,
            "Content-Type": "application/json"
        }
        
        print("\n=== 测试添加物料API ===")
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应信息
        print(f"状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容: {response.text}")
        
        # 尝试解析JSON响应
        try:
            result = response.json()
            print(f"JSON响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        except json.JSONDecodeError:
            print("响应不是有效的JSON格式")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_query_project():
    """
    测试查询项目API接口，确认项目ID是否正确
    """
    # 获取token
    token = get_new_token()
    if not token:
        print("获取Token失败，无法继续测试")
        return
    
    # 读取Excel中的项目ID
    try:
        df = pd.read_excel('hc1.xlsx')
        project_id = str(df.iloc[0]['项目id'])
        project_code = str(df.iloc[0]['项目编码'])
        
        # API 接口URL
        url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service"
        
        # 请求参数
        params = {
            "keyword": project_code
        }
        
        # 请求头
        headers = {
            "x-access-token": token,
            "Content-Type": "application/json"
        }
        
        print("\n=== 测试查询项目API ===")
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        print(f"Excel中的项目ID: {project_id}")
        
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 打印响应信息
        print(f"状态码: {response.status_code}")
        print(f"响应内容长度: {len(response.text)} 字符")
        
        # 尝试解析JSON响应
        try:
            result = response.json()
            if "content" in result and result["content"]:
                api_project_id = result["content"][0].get("id", "")
                print(f"API返回的项目ID: {api_project_id}")
                print(f"项目ID是否匹配: {api_project_id == project_id}")
                
                # 打印项目详情
                project = result["content"][0]
                print(f"项目名称: {project.get('name')}")
                print(f"项目编码: {project.get('code')}")
            else:
                print("未找到项目信息")
        except json.JSONDecodeError:
            print("响应不是有效的JSON格式")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 首先测试查询项目
    test_query_project()
    
    # 然后测试添加物料
    test_add_material_api() 